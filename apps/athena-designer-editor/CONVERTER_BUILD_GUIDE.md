# LcdpConverterManager 独立构建指南

## 概述

本指南介绍如何将 `LcdpConverterManager` 单例从 athena-designer-editor 中独立构建出来，供外部项目使用。

## 构建步骤

### 1. 运行构建命令

```bash
cd apps/athena-designer-editor
npm run build:converter
```

这个命令会：
1. 使用专门的构建配置 `build.converter.json` 构建转换器
2. 生成 UMD 格式的 JavaScript 文件
3. 复制构建产物到 `converter-standalone` 目录
4. 创建使用说明和 package.json

### 2. 构建产物

构建完成后，`converter-standalone` 目录包含：

```
converter-standalone/
├── js/
│   └── lcdp-converter.js     # 主要的转换器文件 (UMD格式)
├── css/
│   └── lcdp-converter.css    # 样式文件 (如果有)
├── package.json              # NPM 包配置
├── README.md                 # 使用说明
└── test.html                 # 测试页面
```

## 使用方法

### 在浏览器中使用

```html
<!DOCTYPE html>
<html>
<head>
    <!-- 引入依赖 -->
    <script src="https://unpkg.com/@alilc/lowcode-types@latest/dist/index.js"></script>
    <!-- 引入转换器 -->
    <script src="./js/lcdp-converter.js"></script>
</head>
<body>
    <script>
        // 使用默认实例
        const converter = window.LcdpConverter.default;
        
        // 示例数据
        const schemaData = {
            componentName: 'COMMON',
            props: {
                dslInfo: {
                    type: 'COMMON',
                    id: 'test-1',
                    props: { text: 'Hello World' }
                }
            },
            children: []
        };
        
        // 转换 schema 到 dsl
        const dslData = converter.toDsl(schemaData);
        console.log('DSL:', dslData);
        
        // 转换 dsl 到 schema
        const schemaResult = converter.toSchema([dslData]);
        console.log('Schema:', schemaResult);
    </script>
</body>
</html>
```

### 在 Node.js 中使用

```javascript
// 如果作为 NPM 包安装
const { lcdpConverterManager, LcdpConverterManager } = require('lcdp-converter-standalone');

// 使用默认实例
const dslData = lcdpConverterManager.toDsl(schemaData);
const schemaData = lcdpConverterManager.toSchema(dslData);

// 创建自定义实例
const customConverter = new LcdpConverterManager();
```

## API 说明

### LcdpConverterManager 类

#### 构造函数
```typescript
constructor(converterList?: Converter[])
```

#### 主要方法

- `toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[]`
  - 将 lowcode schema 转换为 DSL 格式

- `toSchema(dsl: DslData[]): DslSchema[]`
  - 将 DSL 格式转换为 lowcode schema

- `setConverter(key: string, converter: Converter): void`
  - 设置或更新转换器

- `deleteConverter(key: string): void`
  - 删除转换器

- `getConverter(key?: string): Converter`
  - 获取转换器

## 测试

1. 打开 `converter-standalone/test.html` 在浏览器中测试
2. 页面会自动检查依赖加载状态
3. 点击测试按钮验证转换功能

## 部署到其他项目

### 方法1: 直接复制文件
```bash
# 复制整个 converter-standalone 目录到目标项目
cp -r converter-standalone /path/to/your/project/
```

### 方法2: 作为 NPM 包
```bash
# 在 converter-standalone 目录中
npm pack

# 在目标项目中安装
npm install /path/to/lcdp-converter-standalone-1.0.0.tgz
```

## 注意事项

1. **依赖要求**: 确保在使用前已加载 `@alilc/lowcode-types`
2. **兼容性**: 构建产物使用 UMD 格式，支持 AMD、CommonJS 和全局变量
3. **类型支持**: 如需 TypeScript 支持，可以从原项目复制类型定义文件
4. **版本同步**: 当原项目中的转换器更新时，需要重新构建

## 自定义构建

如需修改构建配置，可以编辑：
- `build.converter.json` - 构建配置
- `src/tools/business/lcdp-converter/standalone.ts` - 入口文件
- `scripts/build-converter.js` - 后处理脚本

## 故障排除

### 常见问题

1. **转换器未加载**
   - 检查 `@alilc/lowcode-types` 是否正确加载
   - 确认 `lcdp-converter.js` 文件路径正确

2. **转换失败**
   - 检查输入数据格式是否正确
   - 查看浏览器控制台错误信息

3. **类型错误**
   - 确保使用正确的数据结构
   - 参考测试文件中的示例数据

### 调试建议

1. 使用 `test.html` 进行基础功能测试
2. 检查浏览器控制台的错误信息
3. 对比原项目中的使用方式

## 更新流程

当需要更新转换器时：

1. 在原项目中进行修改
2. 运行 `npm run build:converter` 重新构建
3. 测试新版本功能
4. 部署到目标项目

这样就完成了 LcdpConverterManager 的独立构建和部署！

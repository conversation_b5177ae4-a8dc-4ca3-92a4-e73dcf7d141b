// 独立构建的 LcdpConverterManager 入口文件
// 这个文件专门用于单独构建转换器，供外部使用

import { AthComponentType, Converter, DslData, DslSchema } from './type';
import { commonAthConverter } from './components/common';
import { athenaTableAthConverter } from './components/athena-table';
import { athCollapseItemConverter } from './components/ath-collapse-item';
import { tableGroupAthConverter } from './components/table-group';
import { athLayoutConverter } from './components/ath-layout';
import { athLayoutChildConverter } from './components/ath-layout-child';
import { athCollapseConverter } from './components/ath-collapse';
import { athFlexibleBoxConverter } from './components/ath-flexible-box';
import { tabsAthConverter } from './components/tabs';
import { tabPanelAthConverter } from './components/tab-panel';
import { athGridsterConverter } from './components/ath-gridster';
import { athGridsterChildConverter } from './components/ath-gridster-child';
import { athNameCodeConverter } from './components/ath-name-code';
import { athNewOldConverter } from './components/ath-new-old';
import { lcdpListConverter } from './components/lcdp-list-converter';
import { LcdpFormListConverter } from './components/lcdp-form-list';
import { LcdpModalConverter } from './components/lcdp-modal';
import { allButtonConverter } from './components/button';
import { buttonGroupConverter } from './components/button-group';
import { lcdpFlexConverter } from './components/lcdp-flex-converter';
import { lcdpFlexItemConverter } from './components/lcdp-flex-item-converter';
import { lcdpDataQueryConverter } from './components/lcdp-data-query';
import { lcdpDataQueryItemConverter } from './components/lcdp-data-query-item';
import { lcdpDynamicOperationConverter } from './components/lcdp-dynamic-operation';
import { LcdpInputConverter } from './components/lcdp-input';
import { LcdpLibButtonConverter } from './components/lcdp-lib-button';
import { LcdpFooterButtonGroupConverter } from './components/lcdp-footer-button-group-converter';

class LcdpConverterManager {
  private converterMap = new Map<string, Converter>();
  constructor(converterList: Converter[] = []) {
    this.converterMap = new Map(converterList.map((item) => [item.key, item]));
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToDsl(schema: DslSchema): DslSchema {
    return { ...schema };
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToSchema(dsl: DslData): DslData {
    return { ...dsl };
  }

  setConverter(key: string, athConverter: Converter): void {
    this.converterMap.set(key, athConverter);
  }
  deleteConverter(key: string): void {
    this.converterMap.delete(key);
  }
  getConverter(key: string = AthComponentType.COMMON): Converter {
    return this.converterMap.get(key) ?? commonAthConverter;
  }

  private toObjectDsl(schema: DslSchema): DslData {
    const schemaData = this.beforeToDsl(schema);
    const converter = this.getConverter(schemaData.componentName);
    const { data: dslData, childrenData = [], customTransfer } = converter.toDsl(schemaData);
    if (customTransfer) {
      const childDsl = this.toDsl(customTransfer.data);
      customTransfer.callback(dslData, childDsl);
    } else {
      childrenData.forEach((childrenItem) => {
        let target = dslData;
        if (childrenItem.keyPath && childrenItem.keyPath.length > 0) {
          target = childrenItem.keyPath.reduce((acc, key) => {
            if (!acc[key]) {
              acc[key] = {};
            }
            return acc[key];
          }, dslData);
        }
        target[childrenItem.key] = this.toDsl(childrenItem.data);
      });
    }
    return dslData;
  }

  // lowcode的标准schema转dsl
  toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[] {
    if (Object.prototype.toString.call(schema) === '[object Array]') {
      return schema.map((schemaOrigin: DslSchema) => {
        return this.toObjectDsl(schemaOrigin);
      });
    }
    return this.toObjectDsl(schema as DslSchema);
  }

  // dsl转lowcode的标准schema
  toSchema(dsl: DslData[]): DslSchema[] {
    return dsl.map((dslOrigin: DslData) => {
      const dslData = this.beforeToSchema(dslOrigin);
      const converter = this.getConverter(dslData.type);
      const { data: dslSchema, childrenData = [] } = converter.toSchema(dslData);
      childrenData.forEach((childrenItem) => {
        dslSchema[childrenItem.key] = this.toSchema(childrenItem.data);
      });
      return dslSchema;
    });
  }
}

// 创建单例实例
export const lcdpConverterManager = new LcdpConverterManager([
  commonAthConverter,
  athenaTableAthConverter,
  athCollapseItemConverter,
  tableGroupAthConverter,
  athLayoutConverter,
  athLayoutChildConverter,
  athCollapseConverter,
  tabsAthConverter,
  tabPanelAthConverter,
  athFlexibleBoxConverter,
  athGridsterConverter,
  athGridsterChildConverter,
  athNameCodeConverter,
  athNewOldConverter,
  lcdpListConverter,
  LcdpFormListConverter,
  LcdpModalConverter,
  ...allButtonConverter,
  buttonGroupConverter,
  lcdpFlexConverter,
  lcdpFlexItemConverter,
  lcdpDataQueryConverter,
  lcdpDataQueryItemConverter,
  lcdpDynamicOperationConverter,
  LcdpInputConverter,
  ...LcdpLibButtonConverter,
  LcdpFooterButtonGroupConverter,
]);

// 导出类和实例
export { LcdpConverterManager };
export * from './type';

// 默认导出单例
export default lcdpConverterManager;

{"entry": {"lcdp-converter": "./src/tools/business/lcdp-converter/standalone.ts"}, "vendor": false, "devServer": {"hot": false}, "publicPath": "/", "outputDir": "dist-converter", "library": "LcdpConverter", "libraryTarget": "umd", "externals": {"@alilc/lowcode-types": {"commonjs": "@alilc/lowcode-types", "commonjs2": "@alilc/lowcode-types", "amd": "@alilc/lowcode-types", "root": "AliLowCodeTypes"}}, "plugins": [["build-plugin-react-app"], "./build.plugin.js"]}
// 测试独立构建的转换器
const path = require('path');
const fs = require('fs');

// 模拟测试数据
const testSchema = {
  componentName: 'COMMON',
  props: {
    dslInfo: {
      type: 'COMMON',
      id: 'test-1',
      props: {
        text: 'Hello World'
      }
    }
  },
  children: []
};

const testDsl = {
  type: 'COMMON',
  id: 'test-1',
  props: {
    text: 'Hello World'
  }
};

console.log('🧪 测试数据准备完成');
console.log('Schema:', JSON.stringify(testSchema, null, 2));
console.log('DSL:', JSON.stringify(testDsl, null, 2));

// 检查构建产物是否存在
const converterPath = path.join(__dirname, 'converter-standalone');
if (fs.existsSync(converterPath)) {
  console.log('✅ 独立构建产物存在');
  console.log('📁 构建产物目录:', converterPath);
  
  // 列出构建产物文件
  const files = fs.readdirSync(converterPath);
  console.log('📄 构建产物文件:', files);
} else {
  console.log('❌ 独立构建产物不存在，请先运行: npm run build:converter');
}

console.log('\n📋 使用说明:');
console.log('1. 运行 npm run build:converter 来构建独立版本');
console.log('2. 构建完成后，converter-standalone 目录包含所有需要的文件');
console.log('3. 可以将整个 converter-standalone 目录复制到其他项目中使用');
console.log('4. 参考 converter-standalone/README.md 了解具体使用方法');

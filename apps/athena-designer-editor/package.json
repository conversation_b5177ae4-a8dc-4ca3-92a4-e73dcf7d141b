{"name": "<PERSON><PERSON>a-designer-editor", "author": "happyzzy", "version": "1.0.0", "description": "<PERSON><PERSON>a-designer-editor", "license": "MIT", "main": "index.js", "scripts": {"start": "build-scripts start --disable-reload --port 5557", "prestart": "node syncHooks.js", "build": "build-scripts build", "build:converter": "build-scripts build --config build.converter.json && node scripts/build-converter.js", "prebuild": "node syncHooks.js", "prepublishOnly": "npm run build", "pub": "node ./scripts/watchdog.js && npm pub"}, "files": ["build"], "config": {}, "dependencies": {"@alilc/lowcode-datasource-fetch-handler": "^1.0.1", "@alilc/lowcode-engine": "^1.1.4", "@alilc/lowcode-engine-ext": "^1.0.5", "@alilc/lowcode-plugin-code-editor": "^1.0.3", "@alilc/lowcode-plugin-code-generator": "^1.0.4", "@alilc/lowcode-plugin-components-pane": "^1.0.4", "@alilc/lowcode-plugin-datasource-pane": "^1.0.9", "@alilc/lowcode-plugin-inject": "^1.2.1", "@alilc/lowcode-plugin-manual": "^1.0.4", "@alilc/lowcode-plugin-schema": "^1.0.2", "@alilc/lowcode-plugin-set-ref-prop": "^1.0.1", "@alilc/lowcode-plugin-simulator-select": "^1.0.2", "@alilc/lowcode-plugin-undo-redo": "^1.0.0", "@alilc/lowcode-plugin-zh-en": "^1.0.0", "@alilc/lowcode-react-renderer": "^1.1.2", "@alilc/lowcode-setter-behavior": "^1.0.0", "@alilc/lowcode-setter-title": "^1.0.2", "@alilc/lowcode-types": "^1.1.4", "@ant-design/icons": "5.3.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@module-federation/runtime": "^0.6.1", "antd": "5.15.2", "i18next": "^23.9.0", "i18next-http-backend": "^2.4.3", "lodash": "^4.17.21", "moment": "^2.29.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-i18next": "^14.0.5", "uuid": "^8.3.2", "reflect-metadata": "~0.2.2"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@types/events": "^3.0.0", "@types/react": "^16.8.3", "@types/react-dom": "^16.8.2", "@types/streamsaver": "^2.0.0", "@types/uuid": "^8.3.4", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "build-plugin-react-app": "^1.1.2", "fs-extra": "^10.0.1", "tsconfig-paths-webpack-plugin": "^3.2.0"}, "resolutions": {"@babel/core": "7.20.12"}}
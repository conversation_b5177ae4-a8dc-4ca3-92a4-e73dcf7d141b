const fs = require('fs-extra');
const path = require('path');

async function buildConverter() {
  const sourceDir = path.join(__dirname, '../dist-converter');
  const targetDir = path.join(__dirname, '../converter-standalone');
  
  try {
    // 确保目标目录存在
    await fs.ensureDir(targetDir);
    
    // 复制构建产物
    if (await fs.pathExists(sourceDir)) {
      await fs.copy(sourceDir, targetDir);
      console.log('✅ LcdpConverter 独立构建产物已复制到 converter-standalone 目录');
    } else {
      console.error('❌ 构建产物目录不存在，请先运行 npm run build:converter');
      process.exit(1);
    }
    
    // 创建使用说明文件
    const readmeContent = `# LCDP Converter 独立版本

这是从 athena-designer-editor 中提取的 LcdpConverterManager 独立版本。

## 使用方法

### 在浏览器中使用

\`\`\`html
<!-- 引入依赖 -->
<script src="https://unpkg.com/@alilc/lowcode-types@latest/dist/index.js"></script>
<!-- 引入转换器 -->
<script src="./lcdp-converter.js"></script>

<script>
  // 使用默认实例
  const converter = window.LcdpConverter.default;
  
  // 或者创建新实例
  const customConverter = new window.LcdpConverter.LcdpConverterManager();
  
  // 转换 schema 到 dsl
  const dslData = converter.toDsl(schemaData);
  
  // 转换 dsl 到 schema
  const schemaData = converter.toSchema(dslData);
</script>
\`\`\`

### 在 Node.js 中使用

\`\`\`javascript
const { lcdpConverterManager, LcdpConverterManager } = require('./lcdp-converter.js');

// 使用默认实例
const dslData = lcdpConverterManager.toDsl(schemaData);
const schemaData = lcdpConverterManager.toSchema(dslData);

// 或者创建自定义实例
const customConverter = new LcdpConverterManager();
\`\`\`

## API 说明

### LcdpConverterManager

#### 方法

- \`toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[]\` - 将 lowcode schema 转换为 DSL
- \`toSchema(dsl: DslData[]): DslSchema[]\` - 将 DSL 转换为 lowcode schema
- \`setConverter(key: string, converter: Converter): void\` - 设置转换器
- \`deleteConverter(key: string): void\` - 删除转换器
- \`getConverter(key?: string): Converter\` - 获取转换器

## 依赖

- @alilc/lowcode-types: 用于类型定义

## 注意事项

1. 确保在使用前已经加载了 @alilc/lowcode-types
2. 该版本移除了对 i18next 和其他编辑器特定依赖的引用
3. 如需自定义转换器，请参考源码中的转换器实现
`;

    await fs.writeFile(path.join(targetDir, 'README.md'), readmeContent);
    
    // 创建 package.json
    const packageJson = {
      name: "lcdp-converter-standalone",
      version: "1.0.0",
      description: "Standalone LCDP Converter extracted from athena-designer-editor",
      main: "lcdp-converter.js",
      types: "lcdp-converter.d.ts",
      keywords: ["lcdp", "converter", "lowcode", "dsl", "schema"],
      author: "happyzzy",
      license: "MIT",
      peerDependencies: {
        "@alilc/lowcode-types": "^1.1.4"
      }
    };
    
    await fs.writeFile(
      path.join(targetDir, 'package.json'), 
      JSON.stringify(packageJson, null, 2)
    );
    
    console.log('✅ 已创建 README.md 和 package.json');
    console.log(`📦 独立版本已准备完成，位于: ${targetDir}`);
    
  } catch (error) {
    console.error('❌ 构建过程中出现错误:', error);
    process.exit(1);
  }
}

buildConverter();

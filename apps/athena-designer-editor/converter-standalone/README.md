# LCDP Converter 独立版本

这是从 athena-designer-editor 中提取的 LcdpConverterManager 独立版本。

## 使用方法

### 在浏览器中使用

```html
<!-- 引入依赖 -->
<script src="https://unpkg.com/@alilc/lowcode-types@latest/dist/index.js"></script>
<!-- 引入转换器 -->
<script src="./lcdp-converter.js"></script>

<script>
  // 使用默认实例
  const converter = window.LcdpConverter.default;
  
  // 或者创建新实例
  const customConverter = new window.LcdpConverter.LcdpConverterManager();
  
  // 转换 schema 到 dsl
  const dslData = converter.toDsl(schemaData);
  
  // 转换 dsl 到 schema
  const schemaData = converter.toSchema(dslData);
</script>
```

### 在 Node.js 中使用

```javascript
const { lcdpConverterManager, LcdpConverterManager } = require('./lcdp-converter.js');

// 使用默认实例
const dslData = lcdpConverterManager.toDsl(schemaData);
const schemaData = lcdpConverterManager.toSchema(dslData);

// 或者创建自定义实例
const customConverter = new LcdpConverterManager();
```

## API 说明

### LcdpConverterManager

#### 方法

- `toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[]` - 将 lowcode schema 转换为 DSL
- `toSchema(dsl: DslData[]): DslSchema[]` - 将 DSL 转换为 lowcode schema
- `setConverter(key: string, converter: Converter): void` - 设置转换器
- `deleteConverter(key: string): void` - 删除转换器
- `getConverter(key?: string): Converter` - 获取转换器

## 依赖

- @alilc/lowcode-types: 用于类型定义

## 注意事项

1. 确保在使用前已经加载了 @alilc/lowcode-types
2. 该版本移除了对 i18next 和其他编辑器特定依赖的引用
3. 如需自定义转换器，请参考源码中的转换器实现

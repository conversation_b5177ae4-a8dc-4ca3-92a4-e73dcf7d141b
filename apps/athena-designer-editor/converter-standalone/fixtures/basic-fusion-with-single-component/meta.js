!function e(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.M220318Meta=n():t.M220318Meta=n()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function t(){return e.default}:function t(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t,n){"use strict";n.r(t),n.d(t,"components",(function(){return S})),n.d(t,"componentList",(function(){return O}));var r={componentName:"M220318",title:"M220318",docUrl:"",screenshot:"",devMode:"proCode",npm:{package:"m-220318",version:"0.1.0",exportName:"default",main:"src/index.tsx",destructuring:!1,subName:""},configure:{props:[{title:{label:{type:"i18n","en-US":"title","zh-CN":"title"}},name:"title",setter:{componentName:"StringSetter",isRequired:!0,initialValue:""}},{title:{label:{type:"i18n","en-US":"content","zh-CN":"content"}},name:"content",setter:{componentName:"StringSetter",isRequired:!0,initialValue:""}},{title:{label:{type:"i18n","en-US":"ref","zh-CN":"ref"}},name:"ref",setter:{componentName:"MixedSetter",props:{setters:[{componentName:"FunctionSetter"},{componentName:"ObjectSetter",props:{config:{extraSetter:{componentName:"MixedSetter",isRequired:!1,props:{}}}},isRequired:!1,initialValue:{}}]}}},{title:{label:{type:"i18n","en-US":"key","zh-CN":"key"}},name:"key",setter:{componentName:"MixedSetter",props:{setters:[{componentName:"StringSetter",isRequired:!1,initialValue:""},{componentName:"NumberSetter",isRequired:!1,initialValue:0}]}}}],supports:{style:!0},component:{}}},o=[{title:"M220318",screenshot:"",schema:{componentName:"M220318",props:{}}}],i=Object.assign(Object.assign({},r),{snippets:o});function c(e){return l(e)||p(e)||u(e)||a()}function a(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function l(e){if(Array.isArray(e))return s(e)}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var b={};function g(e){var t=[{title:"\u5e38\u7528",icon:"",children:[]},{title:"\u5bb9\u5668",icon:"",children:[]},{title:"\u5bfc\u822a",icon:"",children:[]},{title:"\u5185\u5bb9",icon:"",children:[]},{title:"Feedback \u53cd\u9988",icon:"",children:[]}],n={"\u539f\u5b50\u7ec4\u4ef6":!0},r={};return e.forEach((function(e){var o=e.category||"\u5176\u4ed6";e.group&&!r[e.componentName]&&(r[e.componentName]=e.group),e.group&&!n[e.group]&&(n[e.group]=!0);var i=t.find((function(e){return e.title===o}));i||(i={title:o,icon:"",children:[]},t.push(i)),e.snippets&&i.children.push({componentName:e.componentName,title:e.title||e.componentName,sort:{category:i.title,group:r[e.componentName]||"\u539f\u5b50\u7ec4\u4ef6",priority:b[i.title]||0},icon:"",package:e.npm.pkg,snippets:e.snippets||[]})})),t}function h(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"m-220318",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"0.1.0",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{"@alifd/next":"1.25.23","@alifd/meet":"2.6.3",antd:"4.17.3"};if(!e||!n)return e;var o=e.npm;return o?("object"===d(r)&&r[o.package]?e.npm=m(m({},o),{},{version:r[o.package]}):o.package===t&&(e.npm=m(m({},o),{},{version:n})),e):e}["\u57fa\u7840\u5143\u7d20","\u5e03\u5c40\u5bb9\u5668\u7c7b","\u8868\u683c\u7c7b","\u8868\u5355\u8be6\u60c5\u7c7b","\u5e2e\u52a9\u7c7b","\u5bf9\u8bdd\u6846\u7c7b","\u4e1a\u52a1\u7c7b","\u901a\u7528","\u5f15\u5bfc","\u4fe1\u606f\u8f93\u5165","\u4fe1\u606f\u5c55\u793a","\u4fe1\u606f\u53cd\u9988"].reverse().forEach((function(e,t){b[e]=++t}));var v,S=[];[i].forEach((function(e){Array.isArray(e)?S.push.apply(S,c(e.map((function(e){return h(e)})))):e.components?S.push.apply(S,c(e.components.map((function(e){return h(e)})))):S.push(h(e))}));var O=g(S),j=!0}])}));
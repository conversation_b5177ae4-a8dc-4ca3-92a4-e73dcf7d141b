<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCDP Converter 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        pre {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border-left: 4px solid #1890ff;
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .error {
            color: #ff4d4f;
            font-weight: bold;
        }
        .info {
            color: #1890ff;
            font-weight: bold;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 LCDP Converter 独立版本测试</h1>
        <p>这个页面用于测试独立构建的 LcdpConverterManager 是否正常工作。</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h2 class="test-title">📦 依赖加载状态</h2>
            <div id="dependency-status">检查中...</div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🧪 转换器测试</h2>
            <button onclick="testConverter()">运行转换测试</button>
            <button onclick="testCustomConverter()">测试自定义实例</button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">📊 测试数据</h2>
            <h3>Schema 示例:</h3>
            <pre id="schema-example"></pre>
            <h3>DSL 示例:</h3>
            <pre id="dsl-example"></pre>
        </div>
    </div>

    <!-- 依赖加载 -->
    <script src="https://unpkg.com/@alilc/lowcode-types@latest/dist/index.js"></script>
    <script src="./js/lcdp-converter.js"></script>

    <script>
        // 测试数据
        const testSchema = {
            componentName: 'COMMON',
            props: {
                dslInfo: {
                    type: 'COMMON',
                    id: 'test-1',
                    props: {
                        text: 'Hello World'
                    }
                }
            },
            children: []
        };

        const testDsl = [{
            type: 'COMMON',
            id: 'test-1',
            props: {
                text: 'Hello World'
            }
        }];

        // 显示测试数据
        document.getElementById('schema-example').textContent = JSON.stringify(testSchema, null, 2);
        document.getElementById('dsl-example').textContent = JSON.stringify(testDsl, null, 2);

        // 检查依赖
        function checkDependencies() {
            const statusEl = document.getElementById('dependency-status');
            let status = [];

            if (typeof window.AliLowCodeTypes !== 'undefined') {
                status.push('<span class="success">✅ @alilc/lowcode-types 已加载</span>');
            } else {
                status.push('<span class="error">❌ @alilc/lowcode-types 未加载</span>');
            }

            if (typeof window.LcdpConverter !== 'undefined') {
                status.push('<span class="success">✅ LcdpConverter 已加载</span>');
            } else {
                status.push('<span class="error">❌ LcdpConverter 未加载</span>');
            }

            if (window.LcdpConverter && window.LcdpConverter.default) {
                status.push('<span class="success">✅ 默认转换器实例可用</span>');
            } else {
                status.push('<span class="error">❌ 默认转换器实例不可用</span>');
            }

            statusEl.innerHTML = status.join('<br>');
        }

        // 测试转换器
        function testConverter() {
            const resultsEl = document.getElementById('test-results');
            let results = [];

            try {
                if (!window.LcdpConverter || !window.LcdpConverter.default) {
                    throw new Error('转换器未正确加载');
                }

                const converter = window.LcdpConverter.default;
                
                // 测试 Schema 到 DSL 转换
                results.push('<h3>🔄 Schema → DSL 转换测试</h3>');
                const dslResult = converter.toDsl(testSchema);
                results.push('<span class="success">✅ 转换成功</span>');
                results.push('<pre>' + JSON.stringify(dslResult, null, 2) + '</pre>');

                // 测试 DSL 到 Schema 转换
                results.push('<h3>🔄 DSL → Schema 转换测试</h3>');
                const schemaResult = converter.toSchema(testDsl);
                results.push('<span class="success">✅ 转换成功</span>');
                results.push('<pre>' + JSON.stringify(schemaResult, null, 2) + '</pre>');

                results.push('<h3>📈 测试总结</h3>');
                results.push('<span class="success">🎉 所有测试通过！转换器工作正常。</span>');

            } catch (error) {
                results.push('<h3>❌ 测试失败</h3>');
                results.push('<span class="error">错误: ' + error.message + '</span>');
                results.push('<pre>' + error.stack + '</pre>');
            }

            resultsEl.innerHTML = results.join('<br>');
        }

        // 测试自定义转换器实例
        function testCustomConverter() {
            const resultsEl = document.getElementById('test-results');
            let results = [];

            try {
                if (!window.LcdpConverter || !window.LcdpConverter.LcdpConverterManager) {
                    throw new Error('LcdpConverterManager 类未正确加载');
                }

                const CustomConverter = window.LcdpConverter.LcdpConverterManager;
                const customConverter = new CustomConverter();
                
                results.push('<h3>🏗️ 自定义转换器实例测试</h3>');
                results.push('<span class="success">✅ 成功创建自定义转换器实例</span>');
                
                // 测试转换
                const dslResult = customConverter.toDsl(testSchema);
                results.push('<span class="success">✅ 自定义实例转换成功</span>');
                results.push('<pre>' + JSON.stringify(dslResult, null, 2) + '</pre>');

            } catch (error) {
                results.push('<h3>❌ 自定义实例测试失败</h3>');
                results.push('<span class="error">错误: ' + error.message + '</span>');
            }

            resultsEl.innerHTML = results.join('<br>');
        }

        // 页面加载完成后检查依赖
        window.addEventListener('load', function() {
            setTimeout(checkDependencies, 1000); // 等待脚本加载
        });
    </script>
</body>
</html>

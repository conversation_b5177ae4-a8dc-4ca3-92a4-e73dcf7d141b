::ng-deep.choose-dictionary-modal-wrapper {
  .choose-dictionary-tabs-wrapper {
  }
  .table-body {
    .search-input {
      width: 486px;
      .ant-input-group-addon:first-child {
        border-left: 0;
      }
    }
    .select-system-id {
      width: 140px;
      min-width: 140px;
      background-color: #fff;
    }
    .search-select {
      width: 140px;
      min-width: 140px;
      background-color: #fff;
      .ant-input-affix-wrapper {
        border-radius: 0px;
      }
      .ant-select-selector {
        border-right: 0 !important;
      }
    }
    .no-data {
      width: 100%;
      height: 400px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      img {
        width: 200px;
        height: 110px;
      }
      .text {
        font-size: 13px;
        color: #666666;
      }
    }
    .ant-pagination-total-text {
      font-size: 13px;
      margin-right: 0;
    }

    // .ant-pagination-total-text {
    //   padding-right: 8px;
    // }
    // .ant-pagination-item {
    //   border: 1px solid transparent !important;
    // }
    // .ant-pagination-prev {
    //   display: inline-flex;
    //   height: 28px !important;
    //   line-height: 28px !important;
    //   svg {
    //     cursor: inherit !important;
    //   }
    //   background: #fff;
    //   margin-top: 1px;
    // }
    // .ant-pagination-simple-pager {
    //   display: inline-flex;
    //   height: 28px !important;
    //   line-height: 28px !important;
    //   margin-top: 1px;
    // }
    // .ant-pagination-next {
    //   display: inline-flex;
    //   height: 28px !important;
    //   line-height: 28px !important;
    //   svg {
    //     cursor: inherit !important;
    //   }
    //   background: #fff;
    //   margin-top: 1px;
    // }
    // .ant-pagination-item-link {
    //   height: 28px !important;
    // }
    // .ant-pagination-item-link:not(:hover) {
    //   background-color: #fff !important;
    // }
    // .ant-pagination-disabled .ant-pagination-item-link:hover {
    //   background-color: #fff !important;
    // }

    /* 全局样式或组件内样式 */
    ::ng-deep .ant-pagination {
      display: flex;
      flex-wrap: nowrap; /* 禁止换行 */
      gap: 4px; /* 缩小间距 */
    }

    ::ng-deep .ant-pagination-total-text {
      white-space: nowrap; /* 总量文本不换行 */
      margin-right: 8px;
    }

    ::ng-deep .ant-pagination-options {
      margin-left: 8px;
    }
  }
  .red-tip {
    color: #ea3d46;
    font-size: 13px;
  }
  .modal-footer {
    text-align: center;
    justify-content: center;
    padding-top: 30px;
    border: 0;
    button {
      width: 88px;
      height: 32px;
      border-radius: 20px;
      margin: 0 11px;

      &:not(.ant-btn-primary) {
        border-color: #6a4cff;
        color: #6a4cff;

        &:hover {
          border-color: #5a4fee;
          color: #5a4fee;
        }
      }
    }
  }
}

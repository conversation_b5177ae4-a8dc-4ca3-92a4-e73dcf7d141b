import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { isNone, isNotNone } from 'common/utils/core.utils';
import { ChooseDictionaryModalService, IDictionary } from './choose-dictionary-modal.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-choose-dictionary-modal',
  templateUrl: './choose-dictionary-modal.component.html',
  styleUrls: ['./choose-dictionary-modal.component.less'],
})
export class ChooseDictionaryModalComponent implements OnInit {
  @Input() visible: boolean;
  @Input() id: number;
  @Input() isPrivatization: boolean;
  @Output() cancel: EventEmitter<any> = new EventEmitter();
  @Output() confirm: EventEmitter<any> = new EventEmitter();

  isNone = isNone;
  isNotNone = isNotNone;

  loading: boolean;
  valueLoading: boolean;

  selectedIndex: number = 0;
  tabs: any[] = [
    { label: 'dj-公共数据', value: 'common' },
    { label: 'dj-团队数据', value: 'team' },
  ];

  searchValue: any; // 搜索关键字
  systemIdList: any[] = [{ id: '', name: this.translate.instant('dj-全部') }];
  selectSystemId: any = '';
  systemOptions = [
    { value: 'key', label: 'dj-字典编码' },
    { value: 'name', label: 'dj-字典名称' },
  ];
  selectedValue: string = 'key';

  selectedKey: number = null;
  selectedRow: any = null;

  records: any[] = [];
  pageInfo: {
    current: number;
    pageSize: number;
    total: number;
  } = {
    current: 1,
    pageSize: 100,
    total: 0,
  };

  constructor(private service: ChooseDictionaryModalService, public translate: TranslateService) {}

  ngOnInit(): void {
    if (!this.isPrivatization) {
      this.handleSystemRequest();
    } else {
      this.handleTableRequest({
        // 获取字典列表
        systemIdList: [],
        searchFieldName: '',
        condition: '',
        pageIndex: 1,
        pageSize: 10,
        sourcePage: 'modelDriven',
      });
    }
    if (this.id) {
      this.selectedKey = this.id;
      this.handleSelectedRowRequest(this.id);
    }
  }

  /**
   * 获取体系列表handler
   */
  handleSystemRequest(): void {
    this.loading = true;
    this.service.querySystemInfos().subscribe(
      (res) => {
        const { code, data } = res ?? {};
        if (code === 0) {
          if (data.length > 0) {
            this.selectSystemId = '';
            this.systemIdList = [{ id: '', name: this.translate.instant('dj-全部') }, ...data];
            this.handleTableRequest({
              // 获取字典列表
              systemIdList: data.map((item) => item.id),
              searchFieldName: '',
              condition: '',
              pageIndex: 1,
              pageSize: 10,
              sourcePage: 'modelDriven',
            });
          }
        } else {
          this.loading = false;
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  /**
   * 查询字典
   * @param params
   * @param preParams
   */
  handleTableRequest(params: IDictionary): void {
    const currnetActiveKey = this.tabs[this.selectedIndex]?.value;
    if (currnetActiveKey === 'team' || (this.systemIdList ?? []).length > 1 || this.isPrivatization) {
      this.loading = true;
      this.service
        .fetchDictionary({
          ...params,
          systemIdList: currnetActiveKey === 'team' ? [] : params.systemIdList,
        })
        .subscribe(
          (res) => {
            const { code, data } = res ?? {};
            const currentTabKey = this.tabs[this.selectedIndex]?.value;
            if (((!this.isPrivatization && currnetActiveKey === currentTabKey) || this.isPrivatization) && code === 0) {
              const { current, size, total, records } = data;
              this.records = records;
              this.pageInfo = {
                current,
                pageSize: size,
                total,
              };
            }
            this.loading = false;
          },
          () => {
            this.loading = false;
          },
        );
    } else {
      this.loading = false;
      this.records = [];
      this.pageInfo = {
        current: 1,
        pageSize: 100,
        total: 0,
      };
    }
  }

  handleSelectedRowRequest(id: number) {
    this.valueLoading = true;
    this.service.queryByIdOreKey({ dictId: id }).subscribe(
      (res) => {
        const { code, data } = res;
        if (code === 0) {
          this.selectedRow = data;
        }
        this.valueLoading = false;
      },
      () => {
        this.valueLoading = false;
      },
    );
  }

  /**
   * 切换Tab
   * @param e
   */
  handleSelectedIndexChange(e: any): void {
    this.selectSystemId = '';
    this.selectedValue = 'key';
    this.searchValue = '';
    this.records = [];
    this.handleTableRequest({
      // 获取字典列表
      systemIdList: this.systemIdList.slice(1).map((item) => item.id),
      searchFieldName: this.selectedValue,
      condition: '',
      pageIndex: 1,
      pageSize: 10,
      sourcePage: 'modelDriven',
    });
  }

  /**
   * 输入框值改变时触发
   */
  handleInputChanged(): void {
    this.handleTableRequest({
      // 获取字典列表
      systemIdList:
        this.selectSystemId === '' ? this.systemIdList.slice(1).map((item) => item.id) : [this.selectSystemId],
      searchFieldName: this.selectedValue,
      condition: this.searchValue,
      pageIndex: 1,
      pageSize: 10,
      sourcePage: 'modelDriven',
    });
  }

  /**
   * 系统切换
   */
  handleSystemChange(): void {
    this.handleTableRequest({
      // 获取字典列表
      systemIdList:
        this.selectSystemId === '' ? this.systemIdList.slice(1).map((item) => item.id) : [this.selectSystemId],
      searchFieldName: this.selectedValue,
      condition: this.searchValue,
      pageIndex: 1,
      pageSize: 10,
      sourcePage: 'modelDriven',
    });
  }

  /**
   * 搜索字典
   */
  handleSelectChange(): void {
    this.handleTableRequest({
      // 获取字典列表
      systemIdList:
        this.selectSystemId === '' ? this.systemIdList.slice(1).map((item) => item.id) : [this.selectSystemId],
      searchFieldName: this.selectedValue,
      condition: this.searchValue,
      pageIndex: 1,
      pageSize: 10,
      sourcePage: 'modelDriven',
    });
  }

  /**
   * 切换页码
   * @param e
   */
  handlePageIndexChange(e: any) {
    this.pageInfo.current = e;
    this.handleTableRequest({
      systemIdList:
        this.selectSystemId === '' ? this.systemIdList.slice(1).map((item) => item.id) : [this.selectSystemId],
      searchFieldName: this.selectedValue,
      condition: this.searchValue,
      pageIndex: this.pageInfo.current,
      pageSize: this.pageInfo.pageSize,
      sourcePage: 'modelDriven',
    });
  }

  /**
   * 切换分页大小
   * @param e
   */
  handlePageSizeChange(e: any) {
    this.pageInfo.pageSize = e;
    this.handleTableRequest({
      systemIdList:
        this.selectSystemId === '' ? this.systemIdList.slice(1).map((item) => item.id) : [this.selectSystemId],
      searchFieldName: this.selectedValue,
      condition: this.searchValue,
      pageIndex: this.pageInfo.current,
      pageSize: this.pageInfo.pageSize,
      sourcePage: 'modelDriven',
    });
  }
  
  /**
   * 系统名称
   * @param systemId
   * @returns
   */
  getSystemName(systemId: string): string {
    const system = this.systemIdList.find((s) => s.id === systemId);
    return system ? system.name : '';
  }

  /**
   * 选择字典
   * @param data
   */
  handleChangeSelectedKey(data: any): void {
    this.selectedKey = data.id;
    this.selectedRow = data;
  }

  /**
   * 取消
   */
  handleCancel(): void {
    this.visible = false;
    this.cancel.emit();
  }

  /**
   * 确定
   */
  handleConfirm(): void {
    if (isNone(this.selectedKey)) return;
    console.log({ selectedKey: this.selectedKey, selectedRow: this.selectedRow });
    this.confirm.emit({ selectedKey: this.selectedKey, selectedRow: this.selectedRow });
  }
}

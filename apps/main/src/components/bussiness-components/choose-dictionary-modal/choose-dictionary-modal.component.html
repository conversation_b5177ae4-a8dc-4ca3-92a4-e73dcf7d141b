<ad-modal
  nzClassName="choose-dictionary-modal-wrapper"
  [nzWidth]="'1080px'"
  [nzTitle]="'dj-选择字典' | translate"
  [(nzVisible)]="visible"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleCancel()"
>
  <ng-container *adModalContent>
    <ng-container *ngIf="!isPrivatization">
      <ad-tabs
        class="choose-dictionary-tabs-wrapper"
        style="margin-bottom: 20px"
        [navStyle]="'default'"
        [(nzSelectedIndex)]="selectedIndex"
        (nzSelectedIndexChange)="handleSelectedIndexChange($event)"
      >
        <ad-tab *ngFor="let tab of tabs" [nzTitle]="tab.label | translate"></ad-tab>
      </ad-tabs>
    </ng-container>
    <div class="table-body">
      <div style="display: grid; grid-template-columns: 68% 32%">
        <div style="padding-right: 12px; padding-bottom: 16px">
          <div>
            <nz-input-group
              class="search-input"
              [ngStyle]="{ width: selectedIndex === 0 ? '496px' : '356px' }"
              [nzAddOnBefore]="addOnBeforeSelect"
              [nzSuffix]="suffixIconSearch"
            >
              <input
                type="text"
                nz-input
                [placeholder]="'dj-请输入关键字' | translate"
                [(ngModel)]="searchValue"
                (keyup.enter)="handleInputChanged()"
              />
            </nz-input-group>
            <ng-template #addOnBeforeSelect>
              <ad-select
                *ngIf="selectedIndex === 0"
                class="select-system-id"
                [(ngModel)]="selectSystemId"
                [nzAllowClear]="false"
                (ngModelChange)="handleSystemChange()"
                [nzNotFoundContent]="'dj-暂无数据' | translate"
              >
                <ad-option
                  *ngFor="let systemId of systemIdList"
                  [nzLabel]="systemId.name"
                  [nzValue]="systemId.id"
                ></ad-option>
              </ad-select>
              <ad-select
                class="search-select"
                [ngStyle]="{ 'margin-left': selectedIndex === 0 ? '10px' : '-11px' }"
                [(ngModel)]="selectedValue"
                [nzAllowClear]="false"
                (ngModelChange)="handleSelectChange()"
              >
                <ad-option
                  *ngFor="let systemOption of systemOptions"
                  [nzLabel]="systemOption.label | translate"
                  [nzValue]="systemOption.value"
                ></ad-option>
              </ad-select>
            </ng-template>
            <ng-template #suffixIconSearch>
              <i adIcon type="search" (click)="handleInputChanged()"></i>
            </ng-template>
          </div>
        </div>
        <div style="padding-left: 12px; border-left: 1px solid #f0f0f0; line-height: 28px">
          <div *ngIf="isNotNone(selectedKey)">
            <span style="font-weight: 600">{{ 'dj-当前选择' | translate }}：</span>
            <span>
              {{ (selectedRow?.lang?.description?.[('dj-LANG' | translate)] ?? '') + '（' + selectedKey + '）' }}
            </span>
          </div>
        </div>
      </div>
      <div style="display: grid; grid-template-columns: 68% 32%">
        <div style="padding-right: 12px; height: 400px">
          <!-- 表格 -->
          <nz-table
            *ngIf="records?.length || loading"
            #basicTable
            [nzData]="records"
            [nzLoading]="loading"
            [nzFrontPagination]="false"
            [nzShowPagination]="true"
            [nzTotal]="pageInfo.total"
            [nzPageIndex]="pageInfo.current"
            [nzPageSize]="pageInfo.pageSize"
            [nzPageSizeOptions]="[10, 20, 30, 40, 50, 100]"
            (nzPageIndexChange)="handlePageIndexChange($event)"
            (nzPageSizeChange)="handlePageSizeChange($event)"
            [nzShowQuickJumper]="false"
            nzShowSizeChanger
            [nzScroll]="{ y: '300px' }"
            [nzShowTotal]="totalTemplate"
          >
            <thead>
              <tr>
                <th [nzWidth]="'50px'"></th>
                <th nzEllipsis>{{ 'dj-字典编码' | translate }}</th>
                <th nzEllipsis>{{ 'dj-字典名称' | translate }}</th>
                <th nzEllipsis>{{ 'dj-体系' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of records; let i = index">
                <td>
                  <label
                    nz-radio
                    [ngModel]="selectedKey === data.id"
                    (ngModelChange)="handleChangeSelectedKey(data)"
                  ></label>
                </td>
                <td nzEllipsis nz-tooltip [nzTooltipTitle]="data.key">{{ data.key }}</td>
                <td nzEllipsis nz-tooltip [nzTooltipTitle]="data.lang?.description?.[('dj-LANG' | translate)]">
                  {{ data.lang?.description?.[('dj-LANG' | translate)] }}
                </td>
                <td nzEllipsis nz-tooltip [nzTooltipTitle]="getSystemName(data.systemId)">
                  {{ getSystemName(data.systemId) }}
                </td>
              </tr>
            </tbody>
          </nz-table>
          <ng-template #totalTemplate let-total>{{ 'dj-共n项1' | translate: { total: pageInfo.total } }}</ng-template>
          <div *ngIf="!records?.length && !loading" class="no-data">
            <img src="/assets/img/designer/no_data.png" alt="" />
            <div class="text">{{ 'dj-暂无数据' | translate }}</div>
          </div>
        </div>
        <div style="padding-left: 12px; border-left: 1px solid #f0f0f0; max-width: 342px; height: 100%">
          <!-- 表格 -->
          <nz-table
            *ngIf="selectedRow?.values?.length || valuesLoading"
            #basicTable
            [nzData]="selectedRow?.values ?? []"
            [nzLoading]="valuesLoading"
            [nzFrontPagination]="false"
            [nzShowPagination]="false"
            [nzPaginationType]="'small'"
            [nzScroll]="{ y: '300px' }"
            nzSize="small"
          >
            <thead>
              <tr>
                <th nzEllipsis>{{ 'dj-字典代码' | translate }}</th>
                <th nzEllipsis>{{ 'dj-字典名称' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of selectedRow?.values ?? []; let i = index">
                <td nzEllipsis nz-tooltip [nzTooltipTitle]="data.key">{{ data.code }}</td>
                <td nzEllipsis nz-tooltip [nzTooltipTitle]="data.lang?.value?.[('dj-LANG' | translate)] ?? '' ">
                  {{ data.lang?.value?.[('dj-LANG' | translate)] ?? '' }}
                </td>
              </tr>
            </tbody>
          </nz-table>
          <div *ngIf="!selectedRow?.values?.length && !valuesLoading" class="no-data">
            <img src="/assets/img/designer/no_data.png" alt="" />
            <div class="text">{{ 'dj-暂无数据' | translate }}</div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="isNone(selectedKey)" class="red-tip">
      {{ 'dj-请选择一项数据' | translate }}
    </div>
    <div class="modal-footer">
      <button ad-button adType="default" (click)="handleCancel()">
        {{ 'dj-取消' | translate }}
      </button>
      <button ad-button adType="primary" (click)="handleConfirm()">
        {{ 'dj-确定' | translate }}
      </button>
    </div>
  </ng-container>
</ad-modal>

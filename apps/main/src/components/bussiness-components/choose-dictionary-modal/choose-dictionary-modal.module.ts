import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ChooseDictionaryModalComponent } from './choose-dictionary-modal.component';
import { ChooseDictionaryModalService } from './choose-dictionary-modal.service';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';

@NgModule({
  declarations: [ChooseDictionaryModalComponent],
  imports: [
    CommonModule,
    FormsModule,
    AdModalModule,
    TranslateModule,
    AdTabsModule,
    NzInputModule,
    NzTableModule,
    AdButtonModule,
    AdIconModule,
    AdSelectModule,
    NzRadioModule,
    NzPaginationModule,
  ],
  exports: [ChooseDictionaryModalComponent],
  providers: [ChooseDictionaryModalService],
})
export class ChooseDictionaryModalModule {}

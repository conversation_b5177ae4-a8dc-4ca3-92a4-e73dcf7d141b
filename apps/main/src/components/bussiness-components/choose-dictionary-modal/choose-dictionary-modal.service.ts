import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';

export interface IDictionary {
  systemIdList: any[];
  searchFieldName: string;
  condition: string;
  pageIndex: number;
  pageSize: number;
  sourcePage: string;
}

@Injectable()
export class ChooseDictionaryModalService {
  adesignerUrl: string;
  constructor(protected http: HttpClient, protected configService: SystemConfigService) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 分页查询字典列表
   * @param params
   * @returns
   */
  fetchDictionary(params: IDictionary): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dictionary/queryDictionaryPageable`;
    return this.http.post(url, params);
  }

  /**
   * 查询体系列表
   * @returns
   */
  querySystemInfos(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dictionary/querySystemInfos`;
    return this.http.get(url);
  }

  /**
   * 查询指定的词汇
   * @param params
   * @returns
   */
  queryByIdOreKey(params: { dictId: number } | { enumKey: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dictionary/queryByIdOreKey`;
    return this.http.get(url, { params });
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';
import { IGenerateBusinessParamsInfo } from './homework-modal.tool';
import { IGenerateDataviewInputInfo } from '../query-plan-modal/query-plan-modal.tool';

@Injectable()
export class HomeworkModalService {
  adesignerUrl: string;

  constructor(private http: HttpClient, private configService: SystemConfigService) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 查询当前应用可选的关联模型
   */
  queryAssociationModalListsService(appCode: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/modelDriver/modelAssign/queryListV2`;
    return this.http.post(url, {
      application: appCode,
      queryType: 'association',
      type: 'curApp',
    });
  }

  /**
   * 根据关联模型反差可选的分类导航模型列表
   */
  queryNavigationModalListsService(params: { modelCode: string; serviceCode: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/queryNavigateModel`;
    return this.http.post(url, params);
  }

  /**
   * 根据关联模型查询可以覆盖的作业列表
   */
  queryCoverableBusinessListsService(params: {
    application: string;
    modelCode: string;
    serviceCode: string;
  }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/queryPageDesignByModel`;
    return this.http.post(url, params);
  }

  /**
   * 生成新作业
   */
  generateBusinessService(params: IGenerateBusinessParamsInfo): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/generatePageDesignByQueryPlan`;
    return this.http.post(url, params);
  }

  /**
   * 创建作业(分销)
   */
  createPageDesign(params: {
    templateType: string;
    application: string;
    name: string;
    version: string;
    lang: any;
  }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/d/createPageDesign`;
    return this.http.post(url, params);
  }

  /**
   * 根据模型，查询模型下的查询方案列表
   */
  queryDataviewListByModelService(params: {
    modelCode: string;
    serviceCode: string;
    businessCode?: string;
  }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/queryDataViewByModel`;
    return this.http.post(url, params);
  }

  /**
   * 生成查询方案
   */
  generateDataviewService(params: IGenerateDataviewInputInfo): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/generateJustQueryPlan`;
    return this.http.post(url, params);
  }

  /**
   * 批量查询字段树
   */
  batchQueryEspActionFields(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/batchQueryEspActionFields`;
    return this.http.post(url, params);
  }

  /**
   * 查询字段树
   */
  queryEspActionFields(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/queryEspActionFields`;
    return this.http.get(url, { params });
  }
}

import { GenerateBusinessType } from '../homework-modal.tool';

type PageType = 'browse' | 'edit' | 'design';

export interface PageDsl {
  type: PageType;
  dsl: {
    layout: any[];
    hooks: any[];
    rules: any[];
    variables: any[];
    dataConnectors: any[];
    globalSetting: any;
  };
}

export type PageDslTemplateMap = {
  [K in GenerateBusinessType]: PageDsl[];
};

export interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

export interface EspActionField {
  data_type: Category;
  data_name: string;
  fullPath: string;
  children: EspActionField[];
  field?: EspActionField[];
  description: LangObject;
  [propName: string]: any;
}

export type EspActionFieldsMap = {
  [propsName: string]: EspActionField;
};

export interface DataConnector {
  id: string;
  name: string;
  description: string;
  connectType: string;
  runOnPageLoad: boolean;
  lang: Lang;
  option: {
    request: {
      method: string;
      path: string;
      params: any[];
      body: any;
      headers: any[];
      cookies: any[];
    };
    preProcess: {
      type: string;
      script: string;
    };
    postProcess: {
      type: string;
      script: string;
    };
    response: {
      type: 'Esp' | 'Standard';
      meta: any;
    };
  };
  [propsName: string]: any;
}

export const enum AthenaDataType {
  STRING = 'string',
  NUMERIC = 'numeric',
  DATE = 'date',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  TIME = 'time',
  DATETIME = 'datetime',
}

export enum GenerateAthComponentType {
  ATHENA_TABLE = 'ATHENA_TABLE',
  FORM_LIST = 'FORM_LIST',
  SELECT = 'SELECT',
  INPUT = 'INPUT',
  DATEPICKER = 'DATEPICKER',
  TIMEPICKER = 'TIMEPICKER',
  INPUT_NUMBER = 'INPUT_NUMBER',
  TABLE_GROUP = 'TABLE_GROUP',
  DYNAMIC_OPERATION = 'DYNAMIC_OPERATION',
}

export enum DataType {
  OBJECT = 'object',
  BOOLEAN = 'boolean',
  STRING = 'string',
  NUMERIC = 'numeric',
  NUMBER = 'number',
  DATE = 'date',
  DATATIME = 'datetime',
  TIME = 'time',
}

export type Category = 'array' | DataType;

export const CategoryComponentMap: Record<Category, GenerateAthComponentType> = {
  array: GenerateAthComponentType.ATHENA_TABLE,
  [DataType.OBJECT]: GenerateAthComponentType.FORM_LIST,
  [DataType.BOOLEAN]: GenerateAthComponentType.SELECT,
  [DataType.STRING]: GenerateAthComponentType.INPUT,
  [DataType.NUMBER]: GenerateAthComponentType.INPUT_NUMBER,
  [DataType.NUMERIC]: GenerateAthComponentType.INPUT_NUMBER,
  [DataType.DATE]: GenerateAthComponentType.DATEPICKER,
  [DataType.DATATIME]: GenerateAthComponentType.DATEPICKER,
  [DataType.TIME]: GenerateAthComponentType.TIMEPICKER,
};

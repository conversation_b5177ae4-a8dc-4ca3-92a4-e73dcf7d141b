/**
 * 作业创建类型枚举定义
 */
export enum GenerateBusinessType {
  /**
   * 空白
   */
  BLANK = 'blank',
  /**
   * 基础表格+表单详情
   */
  BASIC_TABLE = 'basic_table',
  /**
   * 查询方案表格+表单详情，水平
   */
  QUERY_PLAN_TABLE = 'dataview_table_horizontal',
  /**
   * 查询方案表格+表单详情，垂直
   */
  QUERY_PLAN_VERTICAL_TABLE = 'dataview_table_vertical',
  /**
   * 可编辑表格，即单档多栏
   */
  EDITABLE_TABLE = 'editable_table',
  /**
   * 带分类导航的基础表格+表单详情
   */
  CATEGORY_TABLE = 'tree_basic_table',
  /**
   * 带分类导航的可编辑表格
   */
  CATEGORY_TABLE_EDITABLE = 'tree_editable_table',
}

/**
 *  API添加作业支持的作业类型
 */
export const ApiSupportedHomeWorkTypeSet = new Set([
  GenerateBusinessType.BLANK,
  GenerateBusinessType.BASIC_TABLE,
  GenerateBusinessType.EDITABLE_TABLE,
]);

// 作业类型
export const HomeWorkType = [
  {
    key: GenerateBusinessType.BLANK,
    title: 'dj-空白页',
    imageUrl: 'assets/img/designer/blank_logo.png',
    demoUrl: 'assets/img/designer/blank_demo.png',
  },
  {
    key: GenerateBusinessType.BASIC_TABLE,
    title: 'dj-基础表格+表单详情',
    imageUrl: 'assets/img/designer/basic_table_logo.png',
    demoUrl: 'assets/img/designer/basic_table_demo.png',
  },
  {
    key: GenerateBusinessType.QUERY_PLAN_TABLE,
    title: 'dj-带查询方案的表格',
    subTitle: 'dj-横向布局',
    imageUrl: 'assets/img/designer/query_plan_table_logo.png',
    demoUrl: 'assets/img/designer/query_plan_table_demo.svg',
  },
  {
    key: GenerateBusinessType.QUERY_PLAN_VERTICAL_TABLE,
    title: 'dj-带查询方案的表格',
    subTitle: 'dj-纵向布局',
    imageUrl: 'assets/img/designer/query_plan_table_logo.png',
    demoUrl: 'assets/img/designer/query_plan_vertical_table_demo.svg',
  },
  {
    key: GenerateBusinessType.EDITABLE_TABLE,
    title: 'dj-可编辑表格',
    subTitle: 'dj-单档多栏',
    imageUrl: 'assets/img/designer/editable_table_logo.png',
    demoUrl: 'assets/img/designer/editable_table_demo.svg',
  },
  {
    key: GenerateBusinessType.CATEGORY_TABLE,
    title: 'dj-带分类导航的表格表单详情',
    imageUrl: 'assets/img/designer/category_table_logo.png',
    demoUrl: 'assets/img/designer/category_table_demo.svg',
  },
  {
    key: GenerateBusinessType.CATEGORY_TABLE_EDITABLE,
    title: 'dj-带分类导航的可编辑表格',
    imageUrl: 'assets/img/designer/category_table_form_logo.png',
    demoUrl: 'assets/img/designer/category_table_form_demo.svg',
  },
];

/**
 * 生成作业的操作类型定义
 */
export enum GenerateBusinessOperateType {
  /**
   * 生成新作业
   */
  APPEND = 'append',
  /**
   * 覆盖原有作业
   */
  COVERAGE = 'coverage',
}

/**
 * 生成作业时是在哪个视角下
 */
export enum GenerateBusinessAddSourcetype {
  /**
   * 业务对象视角
   */
  BUSINESS_OBJECT = 'object_perspective',
  /**
   * 资源树视角
   */
  RESOURCE = 'resource_perspective',
}

/**
 * 生成作业还是查询方案
 */
export enum GenerateViewType {
  /**
   * 生成作业
   */
  BUSINESS = 'business',
  /**
   * 生成查询方案
   */
  DATAVIEW = 'query_plan',
}

/**
 * 通过什么创建
 */
export enum GenerateFromType {
  /**
   * 通过API创建
   */
  API = 'api',
  /**
   * 通过模型创建
   */
  MODEL = 'model',
}

/**
 * 弹窗必须的数据定义
 */
export interface IInfoModalDataInfo {
  /**
   * 应用code
   */
  application: string;
  /**
   * 作业code, 业务对象视角新建作业需要传
   */
  businessCode?: string;
  /**
   * 关联模型code, 业务对象视角新建作业需要传
   */
  modelCode?: string;
  /**
   * 关联模型的关联服务code，业务对象视角新建作业需要传
   */
  serviceCode?: string;
  /**
   * 关联模型的类型
   */
  modelType?: string;
}

/**
 * 弹窗的额外数据定义
 */
export interface IInfoModalExtraDataInfo {
  /**
   * 生成作业还是查询方案
   */
  viewType: GenerateViewType;
  /**
   * 通过api还是model创建
   */
  from: GenerateFromType;
  /**
   * 从什么视角创建
   */
  perspective: GenerateBusinessAddSourcetype;
  /**
   * 主要用于区分资源视角的左侧菜单和右上角按钮, true代表来自于右上角按钮
   */
  fromResourceHeader: boolean;
}

/**
 * homework, queryplan弹窗入参定义
 */
export interface IInfoModal {
  /**
   * 显示/隐藏标记位
   */
  visible: boolean;
  /**
   * 弹窗必须的数据
   */
  data: IInfoModalDataInfo;
  /**
   * 弹窗的额外数据
   */
  extraData: IInfoModalExtraDataInfo;
}

export interface INormalAjaxResponse<T, M = unknown> {
  code: number;
  data?: T;
  message?: M;
}

export interface ILangInfo {
  zh_CN: string;
  zh_TW: string;
  en_US: string;
}

export interface ILangCollectInfo {
  [key: string]: ILangInfo;
}

/**
 * 查询关联模型接口返回的模型信息
 */
export interface IAjaxAssociationModelInfo {
  /**
   * 模型ID
   */
  id: number;
  /**
   * 模型Code
   */
  code: string;
  /**
   * 模型名称
   */
  name: string;
  /**
   * 租户ID
   */
  tenantId: string;
  /**
   * 后台服务
   */
  serviceCode: string;
  /**
   * 应用Code
   */
  application: string;
  lang: { [key: string]: ILangInfo };
  /**
   * 模型类型
   */
  modelType: string;
}

/**
 * 生成新作业接口入参定义
 */
export interface IGenerateBusinessParamsInfo {
  /**
   * 关联模型的关联服务code
   */
  serviceCode: string;
  /**
   * 关联模型code
   */
  modelId: string;
  /**
   * 是否依赖地端
   */
  dependOnGroundEnd: boolean;
  /**
   * 权限控制
   */
  authorityPrefix: string;
  /**
   * 生成作业的操作类型
   */
  operateType: GenerateBusinessOperateType;
  /**
   * 应用code
   */
  application: string;
  /**
   * 生成作业时是在哪个视角下
   */
  addSourceType: GenerateBusinessAddSourcetype;
  /**
   * 作业名称
   */
  name: string;
  /**
   * 国际化
   */
  lang: ILangCollectInfo;
  /**
   * 通过API还是模型生成
   */
  workType: GenerateFromType;
  /**
   * 生成作业选择的生成类型
   */
  addPageDesignType?: GenerateBusinessType;
  /**
   * 关联API
   */
  actionId?: string;
  /**
   * 查询方案列表
   */
  dataviewCodes?: string[];
  /**
   * 作业code
   */
  businessCode?: string;
  /**
   * 如果是覆盖原有作业，需要提供覆盖的那个作业的作业code
   */
  coveragePageCode?: string;
  /**
   * 分类导航模型code
   */
  navigateModelCode?: string;
  /**
   * 分类导航模型的关联服务code
   */
  navigateModelServiceCode?: string;
  /**
   * 是否支持分类导航模型（从生产新作业的弹窗场景看，选了分类导航模型就是true，没选就是false）
   */
  supportNavigateFlag?: boolean;
  /**
   * 从API创建时，可能存在的手输的关联模型code
   */
  simpleModelCode?: string;
}

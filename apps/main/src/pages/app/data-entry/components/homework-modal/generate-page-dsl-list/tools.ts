import { cloneDeep, isObject } from 'lodash';
import { GenerateBusinessType } from '../homework-modal.tool';
import { dataConnectorTemplate, pageDslTemplateMap, baseGenerateAthComponentSchemaMap } from './config';
import { EspActionField, LangObject, CategoryComponentMap, EspActionFieldsMap, GenerateAthComponentType } from './type';

// 关于在这里产生dsl数据我个人是不建议的，在这里并没法取到lowcode组件的最新meta信息，且无法复用设计器中现成的拖拽生成逻辑
// 但既然po觉得在进入设计器之前就需要产生dsl，且按照“模版“的概念维护dsl模版，那么还是照做
// 所以在这里，会根据标准场景维护几套静态dsl配置，也就是 以后 会 移动到 数据库里的 dsl 模版
// 依照模版数据组装和调整所需dsl
// 但其实弊端很明显：1.如果dsl结构变动后需要更新模版和处理逻辑 2.无法和设计器复用生成逻辑
// 在这里的generate逻辑一部分来自于模版，一部分来自于静态meta（没法直接用设计器最新的meta）
// 所以后续的逻辑更新，不仅涉及到，模版的更新，还涉及到静态meta的更新
// 我更希望的是模版决定结构，具体内容的插入来自于页面类型，action的字段和meta

export const generatePageDslList = (
  formValues: {
    addPageDesignType: GenerateBusinessType;
    listActionId: string;
    formActionId?: string;
    addActionId?: string;
    editActionId?: string;
    deleteActionId?: string;
  },
  espActionFieldsMap: EspActionFieldsMap,
  lang: string = 'zh_CN',
): any[] => {
  const { addPageDesignType, listActionId, formActionId, addActionId, editActionId, deleteActionId } = formValues;
  const pageDslList = pageDslTemplateMap[addPageDesignType];
  if (!pageDslList) return null;

  switch (addPageDesignType) {
    case GenerateBusinessType.BASIC_TABLE: {
      const listActionEspActionFields = espActionFieldsMap[listActionId];
      const formActionIdEspActionFields = espActionFieldsMap[formActionId];

      console.log('listActionEspActionFields:', listActionEspActionFields);
      console.log('schema:', getNodeSchemaData(listActionEspActionFields, lang));
      console.log(
        'lcdpConverterManager:',
        lcdpConverterManager.toDsl(getNodeSchemaData(listActionEspActionFields, lang)),
      );

      if (!listActionEspActionFields || !formActionIdEspActionFields) return null;

      return pageDslList;
    }
    case GenerateBusinessType.EDITABLE_TABLE: {
      return pageDslList;
    }
    default: {
      return pageDslList;
    }
  }
};

// 通过action组装dataConnectors
export const getDataConnectorByAction = (actionData, espActionFields: EspActionField, lang: string = 'zh_CN') => {
  if (!actionData || !espActionFields) return null;
  const dataConnector = cloneDeep(dataConnectorTemplate);
  dataConnector.name = espActionFields.data_name;
  dataConnector.description = espActionFields.description[lang];
  dataConnector['lang']['description'] = espActionFields.description;
  const digi_service = dataConnector?.option?.request?.headers?.find((s) => s.key === 'digi-service');
  if (isObject(digi_service.value)) {
    digi_service.value.prod = actionData.provider;
    digi_service.value.name = actionData.serviceName;
    digi_service.value = JSON.stringify(digi_service.value);
  }
  dataConnector.option.response.meta = espActionFields;
  return dataConnector;
};

// ============ 移植自动生成逻辑 ============
export function removeLastPart(str: string) {
  const lastDotIndex = str.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return str.substring(0, lastDotIndex);
}

const getPlaceHolder = (type: GenerateAthComponentType, description: LangObject) => {
  if ([GenerateAthComponentType.INPUT].includes(type)) {
    return {
      placeholder: {
        zh_TW: '請輸入' + description['zh_TW'],
        en_US: 'Please Input ' + description['en_US'],
        zh_CN: '请输入' + description['zh_CN'],
      },
    };
  } else if (
    [
      GenerateAthComponentType.SELECT,
      GenerateAthComponentType.DATEPICKER,
      GenerateAthComponentType.TIMEPICKER,
    ].includes(type)
  ) {
    return {
      placeholder: {
        zh_TW: '請選擇' + description['zh_TW'],
        en_US: 'Please Select ' + description['en_US'],
        zh_CN: '请选择' + description['zh_CN'],
      },
    };
  } else {
    return {};
  }
};

const getComponentSchema = (componentName: GenerateAthComponentType, extraDslInfo = {}): any => {
  const componentSnippetDslInfo = baseGenerateAthComponentSchemaMap[componentName]?.props?.dslInfo ?? {};
  return {
    componentName: componentName,
    props: {
      dslInfo: { ...componentSnippetDslInfo, ...extraDslInfo, type: componentName },
    },
  };
};

export const getExtraDslInfo = (node: EspActionField, componentName: GenerateAthComponentType, locale = 'zh_CN') => {
  const { data_type } = node;
  if (data_type === 'array') {
    return {
      tableTitle: node.description[locale],
      lang: {
        tableTitle: node.description,
      },
      schema: node.data_name,
      path: removeLastPart(node.fullPath),
    };
  }

  if (componentName === GenerateAthComponentType.TABLE_GROUP) {
    return {
      headerName: node.description[locale],
      lang: {
        headerName: node.description,
      },
      path: removeLastPart(node.fullPath),
    };
  }

  return {
    headerName: node.description[locale],
    lang: {
      headerName: node.description,
      ...getPlaceHolder(componentName, node.description),
    },
    schema: node.data_name,
    path: removeLastPart(node.fullPath),
  };
};

// 获取组件的dslInfo
export const getNodeSchemaData = (
  node: EspActionField,
  locale = 'zh_CN',
  appointComponentName?: GenerateAthComponentType,
): any => {
  const { data_type } = node;
  const componentName = appointComponentName ?? GenerateAthComponentType[CategoryComponentMap[data_type]];
  const children = (node?.field ?? []).filter(
    (child) => (!child.field || child.field.length === 0) && child.data_name !== 'manage_status',
  );

  const extraDslInfo = getExtraDslInfo(node, componentName, locale);
  const returnData = getComponentSchema(componentName, extraDslInfo);

  if (children.length > 0) {
    returnData.children = children.map((childNode: EspActionField) => {
      if (componentName === GenerateAthComponentType.ATHENA_TABLE) {
        // ATHENA_TABLE 需要组装一层 TABLE_GROUP
        const tableGroupNodeData = getNodeSchemaData(
          {
            ...childNode,
            children: [childNode],
          },
          locale,
          GenerateAthComponentType.TABLE_GROUP,
        );
        console.log('tableGroupNodeData:', tableGroupNodeData);
        return tableGroupNodeData;
      }

      return getNodeSchemaData(childNode, locale);
    });
  }

  if (componentName === GenerateAthComponentType.ATHENA_TABLE || componentName === GenerateAthComponentType.FORM_LIST) {
    returnData.children?.unshift(baseGenerateAthComponentSchemaMap[GenerateAthComponentType.DYNAMIC_OPERATION]);
  }

  return returnData;
};
